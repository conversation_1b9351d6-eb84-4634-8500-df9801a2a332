# 安生低代码平台

🚀 基于 Vue 3 + TypeScript 的现代化低代码开发平台，支持多种应用类型的快速开发和部署。

## ✨ 核心特性

- 🎨 **可视化设计** - 拖拽式组件设计器，所见即所得
- 📱 **多端支持** - H5端和PC端设计器
- 🔧 **应用类型管理** - 支持设备充电端、商城端等多种应用类型
- 🎯 **组件化架构** - 高度模块化的组件系统，易于扩展
- 🚀 **TypeScript支持** - 完整的类型安全保障
- 📦 **Monorepo架构** - 统一的代码管理和构建流程
- 🎨 **增强型颜色选择器** - 支持透明色、渐变色和预设颜色

## 🚀 快速开始

### **环境要求**
- Node.js >= 16.0.0
- pnpm >= 7.0.0

### **安装依赖**
```bash
pnpm install
```

### **启动开发服务器**
```bash
# 启动所有服务
pnpm dev

# 或单独启动
pnpm dev:h5        # H5端 (端口: 3000)
pnpm dev:designer  # PC端设计器 (端口: 3001)
pnpm dev:api       # API服务 (端口: 3002)
```

### **访问地址**
- **H5端**: http://localhost:3000
- **PC端设计器**: http://localhost:3001
- **API服务**: http://localhost:3002

> 💡 **端口说明**: 如果默认端口被占用，Vite会自动分配可用端口

## 📦 项目架构

```
lowcode_as-main/
├── 📱 apps/                    # 应用目录
│   ├── h5/                    # H5端应用 - Vue 3 + 移动端组件
│   │   ├── src/
│   │   │   ├── services/      # 模块化服务层
│   │   │   │   ├── events/    # 事件处理模块
│   │   │   │   ├── AppDataManager.ts # 应用数据管理
│   │   │   │   └── SmartAppIdManager.ts # 智能AppID管理
│   │   │   ├── composables/   # 组合式函数
│   │   │   │   └── useDynamicPage.ts # 动态页面逻辑
│   │   │   ├── components/    # 组件
│   │   │   │   └── SmartTabBar/ # 智能TabBar组件
│   │   │   └── stores/        # 状态管理
│   │   └── ...
│   ├── designer/              # PC端设计器 - Vue 3 + Ant Design Vue
│   │   ├── src/
│   │   │   ├── components/    # 设计器组件
│   │   │   │   ├── ColorPicker.vue # 🆕 增强型颜色选择器
│   │   │   │   ├── ComponentStyleEditor.vue # 样式编辑器
│   │   │   │   └── AppDetail/ # 应用详情管理
│   │   │   ├── stores/        # 状态管理
│   │   │   └── views/         # 页面
│   │   └── ...
│   ├── api/                   # API服务 - Express + TypeScript
│   └── server/                # 后端服务
├── 📦 packages/               # 共享包
│   └── aslib/                 # 核心组件库
│       ├── src/
│       │   ├── core/          # 核心功能
│       │   │   ├── events/    # 事件管理系统
│       │   │   ├── api/       # API适配器
│       │   │   └── renderer/  # 组件渲染引擎
│       │   ├── ui/            # UI组件
│       │   │   ├── components/ # 业务组件
│       │   │   └── managers/  # 数据管理器
│       │   └── hooks/         # 状态管理钩子
│       │       ├── common/    # 通用hooks
│       │       ├── device/    # 设备相关hooks
│       │       └── mall/      # 商城相关hooks
├── 📚 docs/                  # 文档目录

│   └── ...
└── 📜 scripts/               # 构建脚本
```

## 🛠️ 技术栈

### **前端技术**
- **框架**: Vue 3 + Composition API
- **语言**: TypeScript
- **构建工具**: Vite
- **包管理**: pnpm + Monorepo
- **状态管理**: Pinia
- **路由**: Vue Router

### **UI组件**
- **PC端**: Ant Design Vue + 自定义ColorPicker
- **H5端**: 移动端适配组件
- **图标**: Iconify
- **样式**: SCSS + CSS Modules

### **开发工具**
- **代码规范**: ESLint + Prettier
- **构建优化**: Turbo
- **类型检查**: TypeScript
- **版本管理**: Git

## 🌐 应用访问

### **H5端访问方式**
- **首页**：`http://localhost:3000/#/home?appId={应用ID}`
- **设备页面**：`http://localhost:3000/#/device/{页面路径}?appId={应用ID}`
- **登录页面**：`http://localhost:3000/#/device/login`

### **PC端生成链接**
PC端设计器生成的H5链接格式：
```
http://localhost:3000/#/home?appId={应用ID}
```

### **智能路由系统**
- **动态路由解析**：根据页面配置自动选择原生路由或低代码路由
- **应用ID动态获取**：从当前路由上下文获取，支持用户自定义AppID
- **向后兼容**：完全兼容旧版本的路由格式

### **智能TabBar系统**
- **动态配置**：TabBar完全由后台配置驱动
- **多样式支持**：默认、圆角、悬浮、胶囊、现代简约等多种样式
- **状态同步**：设计器修改实时同步到H5端
- **🆕 颜色支持**：支持透明色、渐变背景等高级样式

## 🎯 应用类型

平台支持多种应用类型，每种类型都有独立的页面配置和路由管理：

### **设备充电端 (device)**
- 🏠 首页 - 设备管理主页，智能TabBar导航
- 📦 套餐管理 - 套餐列表、购买、订单
- 💰 余额管理 - 余额充值、明细查询
- 👤 用户中心 - 设备设置、实名认证
- 💳 支付功能 - 多种支付方式支持

### **商城端 (mall)** *开发中*
- 🛍️ 商品管理 - 商品列表、详情、分类
- 🛒 购物功能 - 购物车、订单管理
- 💳 支付系统 - 订单支付、支付结果
- 👤 用户中心 - 个人信息、收货地址

## 🆕 最新功能

### **增强型颜色选择器 v2.0**
- ✅ **透明色支持**: Alpha通道滑块，支持`transparent`、`rgba()`格式
- ✅ **渐变色支持**: 线性渐变、径向渐变，多颜色停止点
- ✅ **预设颜色**: 常用颜色快速选择，包含透明色预设
- ✅ **实时预览**: 颜色和渐变效果实时预览
- ✅ **多端同步**: 设计器设置自动同步到H5端显示

### **智能TabBar v3.0**
- ✅ **完全动态配置**: 所有TabBar配置由API驱动
- ✅ **多样式支持**: 5种内置样式（默认、圆角、悬浮、胶囊、现代）
- ✅ **状态同步优化**: 修改TabBar后自动刷新应用状态
- ✅ **统计修复**: 首页正确计入可配置页面统计

### **分层Hooks架构**
- ✅ **通用Hooks**: API缓存、事件总线、本地存储
- ✅ **设备Hooks**: 设备特定的数据管理
- ✅ **商城Hooks**: 预留商城应用hooks
- ✅ **向后兼容**: 保持现有代码100%兼容



## 📚 文档

| 文档类型 | 链接 | 描述 |
|---------|------|------|
| 📖 **快速开始** | [docs/getting-started/](./docs/getting-started/) | 环境搭建、项目结构、开发指南 |
| 🏗️ **架构文档** | [docs/architecture/](./docs/architecture/) | 系统架构、应用类型管理、模块设计 |
| 📱 **H5端文档** | [docs/h5/](./docs/h5/) | H5应用架构、组件开发、路由管理 |
| 💻 **PC端文档** | [docs/designer/](./docs/designer/) | 设计器架构、组件库、事件系统 |
| 📦 **核心包文档** | [packages/aslib/README.md](./packages/aslib/README.md) | 核心功能、组件库、Hooks系统 |
| 🎨 **UI组件库** | [docs/ui/](./docs/ui/) | 组件库概览、开发规范、主题系统 |


## 🔧 常用命令

```bash
# 开发
pnpm dev                    # 启动所有服务
pnpm dev:h5                # 启动H5端
pnpm dev:designer          # 启动PC端设计器
pnpm dev:api               # 启动API服务

# 构建
pnpm build                 # 构建所有项目
pnpm build:h5              # 构建H5端
pnpm build:designer        # 构建PC端设计器

# 代码检查
pnpm lint                  # 检查所有项目
pnpm lint:fix              # 自动修复代码问题
pnpm type-check            # TypeScript类型检查

# 测试
pnpm test                  # 运行所有测试
pnpm test:unit             # 运行单元测试

# 清理
pnpm clean                 # 清理构建产物
pnpm clean:deps            # 清理依赖
```

## 🌟 核心功能

### **模块化服务架构**
- **事件处理系统**：统一的事件管理，支持导航、自定义代码、预设操作
- **智能AppID管理**：动态AppID验证和管理
- **数据管理器**：设备数据、用户数据的统一管理
- **API适配器**：支持不同应用类型的API适配

### **应用类型管理**
- 统一的应用类型定义和路由管理
- PC端设计器支持应用类型选择
- 动态页面跳转配置
- 类型安全的路由验证

### **组件系统**
- 可视化组件配置和样式编辑
- 统一的事件系统支持
- 增强型颜色选择器（透明色、渐变色）
- 响应式设计和多端适配

### **开发体验**
- 完整的TypeScript支持
- 热重载开发服务器
- 代码规范自动检查
- 统一的构建流程

## 📊 开发状态

- ✅ **基础架构**: 完成
- ✅ **模块化服务架构**: 100% 完成
- ✅ **智能路由系统**: 100% 完成  
- ✅ **事件处理系统**: 100% 完成
- ✅ **增强型颜色选择器**: 100% 完成
- ✅ **智能TabBar系统**: 100% 完成
- ✅ **分层Hooks架构**: 100% 完成
- ✅ **设计器核心**: 95% 完成
- ✅ **H5运行时**: 100% 完成
- ✅ **组件库**: 90% 完成
- ✅ **API集成**: 95% 完成


## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 [MIT License](./LICENSE) 许可证。

---

**版本**: v1.3.0  
**最后更新**: 2024-01-27  
**开发者**: 安生团队

## 🆕 最新更新 (v1.3.0)

### **颜色系统重大升级**
- 🎨 **增强型颜色选择器**: 支持透明色、渐变色、预设颜色
- 🌈 **渐变背景支持**: 线性渐变、径向渐变，多颜色停止点
- 🔍 **实时预览**: 颜色和渐变效果实时预览
- 🔄 **多端同步**: 设计器样式自动同步到H5端

### **TabBar系统优化**
- 🔧 **状态同步修复**: TabBar修改后自动刷新应用状态
- 📊 **统计功能修复**: 首页正确计入可配置页面统计  
- 🎨 **样式增强**: 支持更多TabBar样式和颜色配置
- ⚡ **性能优化**: 减少不必要的重渲染

### **Hooks架构重构**
- 📁 **分层设计**: common/device/mall三层架构
- 🔄 **向后兼容**: 保持现有API 100%兼容
- 📦 **模块化导出**: 支持按需导入和命名空间导出
- 🛡️ **类型安全**: 完整的TypeScript类型定义

