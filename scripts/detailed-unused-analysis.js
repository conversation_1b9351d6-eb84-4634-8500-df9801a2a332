#!/usr/bin/env node

/**
 * 详细的未使用文件分析
 * 考虑动态导入、路由配置等特殊情况
 */

const fs = require('fs')
const path = require('path')

const projectRoot = path.resolve(__dirname, '..')

/**
 * 检查文件是否被动态导入使用
 */
function checkDynamicUsage(filePath) {
  const relativePath = path.relative(projectRoot, filePath)
  const reasons = []

  // 1. 检查是否是Vue页面组件（通过路由动态加载）
  if (relativePath.includes('/pages/') && relativePath.endsWith('.vue')) {
    // 检查是否在路由配置中被引用
    const routerFiles = [
      'apps/h5/src/router/index.ts',
      'packages/aslib/src/core/applications/device/index.ts'
    ]
    
    for (const routerFile of routerFiles) {
      try {
        const routerContent = fs.readFileSync(path.join(projectRoot, routerFile), 'utf8')
        const fileName = path.basename(filePath, '.vue')
        const dirName = path.basename(path.dirname(filePath))
        
        // 检查是否在路由配置中被提及
        if (routerContent.includes(fileName) || routerContent.includes(dirName)) {
          reasons.push(`被路由配置引用: ${routerFile}`)
        }
        
        // 检查动态导入模式
        if (routerContent.includes('import.meta.glob') && 
            routerContent.includes('pages/**/*.vue')) {
          reasons.push(`通过动态导入模式加载: ${routerFile}`)
        }
      } catch (error) {
        // 文件不存在或读取失败
      }
    }
  }

  // 2. 检查是否是hooks文件
  if (relativePath.includes('/hooks/') && relativePath.endsWith('.ts')) {
    // 检查是否在hooks/index.ts中被导出
    try {
      const hooksIndexPath = path.join(projectRoot, 'packages/aslib/src/hooks/index.ts')
      const hooksIndexContent = fs.readFileSync(hooksIndexPath, 'utf8')
      const fileName = path.basename(filePath, '.ts')
      
      if (hooksIndexContent.includes(fileName)) {
        reasons.push('被hooks模块导出')
      }
    } catch (error) {
      // 文件不存在
    }
  }

  // 3. 检查是否是API路由文件
  if (relativePath.includes('/routes/') && relativePath.endsWith('.ts')) {
    try {
      const apiIndexPath = path.join(projectRoot, 'apps/api/src/index.ts')
      if (fs.existsSync(apiIndexPath)) {
        const apiIndexContent = fs.readFileSync(apiIndexPath, 'utf8')
        const fileName = path.basename(filePath, '.ts')
        
        if (apiIndexContent.includes(fileName)) {
          reasons.push('被API服务引用')
        }
      }
    } catch (error) {
      // 文件不存在
    }
  }

  // 4. 检查是否是类型定义文件
  if (relativePath.endsWith('.d.ts') || relativePath.includes('/types/')) {
    reasons.push('类型定义文件（可能被TypeScript编译器使用）')
  }

  // 5. 检查是否是配置文件
  const configPatterns = [
    'auto-imports.d.ts',
    'components.d.ts',
    'env.d.ts'
  ]
  
  if (configPatterns.some(pattern => relativePath.includes(pattern))) {
    reasons.push('自动生成的配置文件')
  }

  return reasons
}

/**
 * 分析特定目录的文件使用情况
 */
function analyzeDirectory(dirPath) {
  const results = {
    totalFiles: 0,
    unusedFiles: [],
    usedFiles: [],
    specialFiles: []
  }

  if (!fs.existsSync(dirPath)) {
    return results
  }

  function scanDir(currentDir) {
    const items = fs.readdirSync(currentDir)
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item)
      const stat = fs.statSync(fullPath)
      
      if (stat.isDirectory()) {
        scanDir(fullPath)
      } else if (stat.isFile() && 
                 (fullPath.endsWith('.ts') || fullPath.endsWith('.vue') || fullPath.endsWith('.js'))) {
        results.totalFiles++
        
        const reasons = checkDynamicUsage(fullPath)
        const relativePath = path.relative(projectRoot, fullPath)
        
        if (reasons.length > 0) {
          results.usedFiles.push({
            path: relativePath,
            reasons: reasons
          })
        } else {
          results.unusedFiles.push(relativePath)
        }
      }
    }
  }

  scanDir(dirPath)
  return results
}

/**
 * 主分析函数
 */
function main() {
  console.log('🔍 详细分析项目中的文件使用情况...\n')

  const analysisTargets = [
    {
      name: 'H5页面组件',
      path: path.join(projectRoot, 'apps/h5/src/modules/device/pages')
    },
    {
      name: 'Hooks文件',
      path: path.join(projectRoot, 'packages/aslib/src/hooks')
    },
    {
      name: 'API路由',
      path: path.join(projectRoot, 'apps/api/src/routes')
    },
    {
      name: 'UI组件',
      path: path.join(projectRoot, 'packages/aslib/src/ui/components')
    }
  ]

  for (const target of analysisTargets) {
    console.log(`📁 分析 ${target.name}:`)
    const result = analyzeDirectory(target.path)
    
    console.log(`  总文件数: ${result.totalFiles}`)
    console.log(`  使用中的文件: ${result.usedFiles.length}`)
    console.log(`  可能未使用: ${result.unusedFiles.length}`)
    
    if (result.usedFiles.length > 0) {
      console.log('\n  ✅ 使用中的文件:')
      result.usedFiles.forEach(file => {
        console.log(`    - ${file.path}`)
        file.reasons.forEach(reason => {
          console.log(`      └─ ${reason}`)
        })
      })
    }
    
    if (result.unusedFiles.length > 0) {
      console.log('\n  ❌ 可能未使用的文件:')
      result.unusedFiles.forEach(file => {
        console.log(`    - ${file}`)
      })
    }
    
    console.log('\n' + '─'.repeat(60) + '\n')
  }

  // 特殊检查：检查脚本文件
  console.log('📜 脚本文件分析:')
  const scriptsDir = path.join(projectRoot, 'scripts')
  const scriptFiles = fs.readdirSync(scriptsDir).filter(f => f.endsWith('.js'))
  
  scriptFiles.forEach(file => {
    const filePath = path.join(scriptsDir, file)
    const packageJsonPath = path.join(projectRoot, 'package.json')
    
    try {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
      const isUsedInScripts = Object.values(packageJson.scripts || {})
        .some(script => script.includes(file))
      
      if (isUsedInScripts) {
        console.log(`  ✅ ${file} - 在package.json中被引用`)
      } else {
        console.log(`  ❌ ${file} - 可能未被使用`)
      }
    } catch (error) {
      console.log(`  ❓ ${file} - 无法确定使用状态`)
    }
  })
}

if (require.main === module) {
  main()
}

module.exports = { checkDynamicUsage, analyzeDirectory }
