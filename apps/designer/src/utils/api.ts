import axios from 'axios'
import type { PageConfig } from '@lowcode/aslib/core'

// 应用配置接口
export interface AppConfig {
  id: string
  slug?: string
  name: string
  description: string
  icon: string
  defaultHomePage: string
  appType?: string  // 应用类型，可选字段以保持向后兼容
  tabBar?: {
    enabled?: boolean
    tabs: TabConfig[]
    type?: string
    style?: {
      backgroundColor?: string
      activeColor?: string
      inactiveColor?: string
      height?: string
      showText?: boolean
      iconTextGap?: string
      borderRadius?: string
    }
  }
  createTime: Date | string
  updateTime: Date | string
  published: number | boolean
  publishTime?: Date | string
  creator_id?: number
  pageConfigs?: any[]
}

export interface TabConfig {
  id: string
  name: string
  label: string
  icon: string
  path: string
  pageId: string
  active?: boolean
}

// 创建axios实例
const api = axios.create({
  baseURL: 'http://localhost:3002/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器 - 自动添加token
api.interceptors.request.use(
  (config) => {
    // 从localStorage获取token
    const token = localStorage.getItem('authorization')
    console.log('🔍 [API] Token调试:', {
      url: config.url,
      method: config.method,
      hasToken: !!token,
      tokenPreview: token ? `${token.substring(0, 20)}...${token.substring(token.length - 20)}` : null,
      tokenLength: token ? token.length : 0
    })

    if (token) {
      // 不使用Bearer前缀，直接设置token
      config.headers.Authorization = token
    } else {
      console.warn('⚠️ [API] 未找到authorization token，请检查localStorage')
    }
    return config
  },
  (error) => {
    console.error('Request Error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    console.error('API Error:', error)
    // 如果是401错误，清除token并跳转到登录页
    if (error.response?.status === 401) {
      localStorage.removeItem('lowcode_token')
      // 可以在这里添加跳转到登录页的逻辑
      console.warn('Token已过期，请重新登录')
    }
    throw error
  }
)

// API接口定义
export interface ApiResponse<T = any> {
  code: number
  data: T
  msg: string
}

// 页面配置相关API
export const pageConfigApi = {
  // 获取页面配置列表
  async getList(params?: { page?: number; pageSize?: number }): Promise<ApiResponse<{
    list: PageConfig[]
    total: number
    page: number
    pageSize: number
  }>> {
    return api.get('/page-config', { params })
  },

  // 获取单个页面配置
  async getById(id: string): Promise<ApiResponse<PageConfig>> {
    return api.get(`/page-config/${id}`)
  },

  // 创建页面配置
  async create(config: Partial<PageConfig>): Promise<ApiResponse<PageConfig>> {
    return api.post('/page-config', config)
  },

  // 更新页面配置
  async update(id: string, config: Partial<PageConfig>): Promise<ApiResponse<PageConfig>> {
    return api.put(`/page-config/${id}`, config)
  },

  // 删除页面配置
  async delete(id: string): Promise<ApiResponse<null>> {
    return api.delete(`/page-config/${id}`)
  },

  // 复制页面配置
  async copy(id: string, newConfig: { newId: string; newName: string; newPath: string }): Promise<ApiResponse<PageConfig>> {
    return api.post(`/page-config/${id}/copy`, newConfig)
  },

  // 发布页面配置
  async publish(id: string): Promise<ApiResponse<PageConfig>> {
    return api.post(`/page-config/${id}/publish`)
  },

  // 🔧 新增：取消发布页面配置
  async unpublish(id: string): Promise<ApiResponse<PageConfig>> {
    return api.post(`/page-config/${id}/unpublish`)
  }
}

// 应用相关API
export const appApi = {
  // 获取应用列表
  async getList(params?: { page?: number; pageSize?: number }): Promise<ApiResponse<{
    list: AppConfig[]
    total: number
    page: number
    pageSize: number
  }>> {
    return api.get('/app', { params })
  },

  // 获取单个应用
  async getById(id: string): Promise<ApiResponse<AppConfig>> {
    return api.get(`/app/${id}`)
  },

  // 获取应用的页面列表
  async getPages(id: string, params?: { page?: number; pageSize?: number }): Promise<ApiResponse<{
    list: PageConfig[]
    total: number
    page: number
    pageSize: number
  }>> {
    return api.get(`/app/${id}/pages`, { params })
  },

  // 创建应用
  async create(app: Partial<AppConfig>): Promise<ApiResponse<AppConfig>> {
    return api.post('/app', app)
  },

  // 更新应用
  async update(id: string, app: Partial<AppConfig>): Promise<ApiResponse<AppConfig>> {
    return api.put(`/app/${id}`, app)
  },

  // 删除应用
  async delete(id: string): Promise<ApiResponse<null>> {
    return api.delete(`/app/${id}`)
  },

  // 发布应用
  async publish(id: string): Promise<ApiResponse<AppConfig>> {
    return api.post(`/app/${id}/publish`)
  },

  // 取消发布应用
  async unpublish(id: string): Promise<ApiResponse<AppConfig>> {
    return api.post(`/app/${id}/unpublish`)
  }
}

export default api
