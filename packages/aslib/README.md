# @lowcode/aslib

> 安生低代码平台核心组件库 - 统一的Vue 3 + TypeScript低代码解决方案

## 📦 包含模块

- **Core**: 低代码渲染引擎、事件管理、API适配
- **UI**: Vue组件库和数据管理器
- **Hooks**: 状态管理和工具函数（分层架构）

## 🚀 安装

```bash
pnpm add @lowcode/aslib
```

## 📖 使用方式

### 方式一：统一导入
```typescript
import AsLib, { Core, UI, Hooks } from '@lowcode/aslib'

// 使用Core模块
const { ComponentRenderer, PageRenderer, EventManager } = Core

// 使用UI模块  
const { HomeBasic, HomeMore, DataManager } = UI

// 使用Hooks模块
const { useGlobalData, useApiCache, useEventBus } = Hooks
```

### 方式二：按模块导入
```typescript
// 从Core模块导入
import { ComponentRenderer, PageRenderer, EventManager } from '@lowcode/aslib/core'

// 从UI模块导入
import { HomeBasic, HomeMore, DataManager } from '@lowcode/aslib/ui'

// 从Hooks模块导入
import { useGlobalData, useApiCache } from '@lowcode/aslib/hooks'
```

### 方式三：分层Hooks导入 🆕
```typescript
// 通用hooks
import { useApiCache, useEventBus, useStorage } from '@lowcode/aslib/hooks'

// 命名空间导入
import { CommonHooks, DeviceHooks, MallHooks } from '@lowcode/aslib/hooks'
const { useApiCache } = CommonHooks
const { useGlobalData } = DeviceHooks

// 向后兼容的导入方式
import { useGlobalData } from '@lowcode/aslib/hooks' // 仍然有效
```

## 🏗️ 核心架构

### **Core 模块**
```typescript
// 组件渲染引擎
import { ComponentRenderer, PageRenderer } from '@lowcode/aslib/core'

// 事件管理系统  
import { EventManager, UnifiedEventManager } from '@lowcode/aslib/core'

// API适配器
import { BaseAdapter, DeviceAdapter } from '@lowcode/aslib/core'

// 类型定义
import type { ComponentConfig, PageConfig, RenderContext } from '@lowcode/aslib/core'
```

### **UI 模块**
```typescript
// 业务组件
import { 
  HomeBasic,      // 设备基础信息
  HomeDetails,    // 设备详情
  HomeMore,       // 快捷功能
  HomeNetWork,    // 网络状态
  HomeRealName    // 实名认证
} from '@lowcode/aslib/ui'

// 数据管理器
import { DataManager } from '@lowcode/aslib/ui'

// 设置API客户端
import { setAPIClient } from '@lowcode/aslib/ui'
```

### **Hooks 模块 🆕 分层架构**
```typescript
// ==================== 通用Hooks ====================
import { 
  useApiCache,     // API缓存管理
  useEventBus,     // 事件总线
  useStorage       // 本地存储
} from '@lowcode/aslib/hooks'

// ==================== 设备相关Hooks ====================
import { 
  useGlobalData    // 设备数据管理（移动到device层）
} from '@lowcode/aslib/hooks'

// ==================== 分层导入 ====================
import { CommonHooks, DeviceHooks, MallHooks } from '@lowcode/aslib/hooks'

// 通用功能
const { useApiCache, useEventBus, useStorage } = CommonHooks

// 设备应用专用
const { useGlobalData } = DeviceHooks

// 商城应用专用（预留）
const { /* 未来的商城hooks */ } = MallHooks
```

## 🎨 组件配置示例

### 基础组件使用
```vue
<template>
  <!-- 使用渲染引擎 -->
  <ComponentRenderer 
    :config="componentConfig"
    :context="renderContext"
    :data-manager="dataManager"
    :event-manager="eventManager"
  />
  
  <!-- 直接使用业务组件 -->
  <HomeBasic :data="deviceData" />
  <HomeMore :actions="quickActions" />
</template>

<script setup lang="ts">
import { ComponentRenderer, DataManager, EventManager } from '@lowcode/aslib/core'
import { HomeBasic, HomeMore } from '@lowcode/aslib/ui'
import { useGlobalData } from '@lowcode/aslib/hooks'

// 使用全局数据管理
const { deviceData } = useGlobalData()

// 初始化管理器
const dataManager = new DataManager()
const eventManager = new EventManager()
</script>
```

### 事件系统使用
```typescript
// 统一事件管理器
import { UnifiedEventManager } from '@lowcode/aslib/core'

const eventManager = new UnifiedEventManager()

// 注册事件处理器
eventManager.register('navigation', {
  handler: async (data) => {
    console.log('导航事件:', data)
    // 处理导航逻辑
  }
})

// 触发事件
eventManager.trigger('navigation', {
  type: 'page',
  target: '/device/details',
  params: { id: 123 }
})
```

## 🔧 开发

```bash
# 安装依赖
pnpm install

# 开发模式
pnpm dev

# 构建
pnpm build

# 类型检查
pnpm type-check

# 代码检查
pnpm lint
```

## 📁 目录结构

```
packages/aslib/
├── src/
│   ├── core/                 # 🏗️ 核心模块
│   │   ├── api/             # API适配器
│   │   │   ├── adapters/    # 具体适配器实现
│   │   │   └── gateway/     # API网关
│   │   ├── events/          # 事件管理系统
│   │   │   ├── EventManager.ts
│   │   │   └── UnifiedEventManager.ts
│   │   ├── renderer/        # 组件渲染引擎
│   │   │   ├── ComponentRenderer.vue
│   │   │   └── PageRenderer.vue
│   │   ├── types/           # 类型定义
│   │   └── index.ts         # Core模块导出
│   ├── ui/                  # 🎨 UI模块
│   │   ├── components/      # 业务组件
│   │   │   ├── Home/        # 首页相关组件
│   │   │   │   ├── HomeBasic/
│   │   │   │   ├── HomeDetails/
│   │   │   │   ├── HomeMore/
│   │   │   │   ├── HomeNetWork/
│   │   │   │   └── HomeRealName/
│   │   ├── managers/        # 数据管理器
│   │   │   └── DataManager.ts
│   │   ├── events/          # UI事件处理
│   │   └── index.ts         # UI模块导出
│   ├── hooks/               # 🎯 Hooks模块（分层架构）
│   │   ├── common/          # 通用hooks
│   │   │   ├── useApiCache.ts
│   │   │   ├── useEventBus.ts
│   │   │   ├── useStorage.ts
│   │   │   └── index.ts
│   │   ├── device/          # 设备应用hooks
│   │   │   ├── useGlobalData.ts
│   │   │   └── index.ts
│   │   ├── mall/            # 商城应用hooks（预留）
│   │   │   └── index.ts
│   │   └── index.ts         # Hooks统一导出
│   └── index.ts             # 📦 根导出文件
├── dist/                    # 构建产物
├── types/                   # 类型声明文件
├── package.json
├── vite.config.ts
├── tsconfig.json
└── README.md
```

## 🌟 核心特性

### **🎨 组件渲染引擎**
- Vue 3 + Composition API
- TypeScript类型安全
- 动态组件加载
- 事件系统集成
- 响应式数据绑定

### **🔧 事件管理系统**
- 统一的事件处理架构
- 支持导航、自定义代码、预设操作
- 类型安全的事件定义
- 向后兼容的API设计

### **🗄️ 数据管理**
- 设备数据统一管理
- API缓存机制
- 本地存储封装
- 响应式状态更新

### **🎯 分层Hooks架构 🆕**
- **common**: 通用功能（API缓存、事件总线、存储）
- **device**: 设备应用专用（设备数据管理）
- **mall**: 商城应用专用（预留扩展）
- **向后兼容**: 保持现有API 100%可用

## 🔄 分层Hooks详细说明

### **通用层 (Common)**
```typescript
// API缓存管理
const { cache, clearCache } = useApiCache({
  ttl: 300000, // 5分钟缓存
  maxSize: 100
})

// 事件总线
const { emit, on, off } = useEventBus()
emit('user-action', { type: 'click', target: 'button' })

// 本地存储
const { get, set, remove } = useStorage('user-preferences')
```

### **设备层 (Device)**
```typescript
// 设备数据管理
const {
  deviceData,        // 设备详情
  setDeviceDetails,  // 设置设备信息
  setDeviceCards,    // 设置设备卡片
  setRealNameCards,  // 设置实名卡片
  setDeviceLoading   // 设置加载状态
} = useGlobalData()
```

### **商城层 (Mall)** 
```typescript
// 预留给商城应用的hooks
// 未来可能包含：商品管理、购物车、订单等
```

## 🎯 迁移说明

### 从独立包迁移
**之前:**
```typescript
import { ComponentRenderer } from '@lowcode/core'
import { HomeBasic } from '@lowcode/ui'  
import { useGlobalData } from '@lowcode/hooks'
```

**现在:**
```typescript
import { ComponentRenderer } from '@lowcode/aslib/core'
import { HomeBasic } from '@lowcode/aslib/ui'
import { useGlobalData } from '@lowcode/aslib/hooks'
```

### Hooks分层迁移
**原有代码保持兼容:**
```typescript
// 这些导入方式仍然有效
import { useGlobalData, useApiCache } from '@lowcode/aslib/hooks'
```

**新的分层导入方式:**
```typescript
// 推荐的新方式
import { CommonHooks, DeviceHooks } from '@lowcode/aslib/hooks'
const { useApiCache } = CommonHooks
const { useGlobalData } = DeviceHooks
```

## 🚀 未来规划



### **组件库扩展**
- 更多业务组件
- 主题定制系统
- 国际化支持
- 无障碍访问

### **性能优化**
- 组件懒加载
- 虚拟滚动
- 代码分割
- 缓存优化

## 📊 版本历史

- **v1.3.0** (2024-01-27)
  - ✅ 分层Hooks架构重构
  - ✅ 向后兼容性保证
  - ✅ TypeScript类型完善
  - ✅ 文档更新完整

- **v1.2.0** (2024-01-20)
  - ✅ 统一事件管理系统
  - ✅ API适配器架构
  - ✅ 组件渲染引擎优化

- **v1.1.0** (2024-01-15)
  - ✅ 核心包合并
  - ✅ 模块化导出
  - ✅ 开发工具链完善

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支: `git checkout -b feature/amazing-feature`
3. 提交更改: `git commit -m 'Add amazing feature'`
4. 推送到分支: `git push origin feature/amazing-feature`
5. 创建 Pull Request

## 📄 许可证

MIT License

---

**维护者**: 安生团队  
**最后更新**: 2024-01-27  
**版本**: v1.3.0